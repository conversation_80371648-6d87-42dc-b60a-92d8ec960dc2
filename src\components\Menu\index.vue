<template>
    <el-tooltip placement="right" trigger="hover" :disabled="!tooltip" :content="tooltip">
        <div v-bind="$attrs" class="relative size-[30px] flex justify-center items-center rounded cursor-pointer"
            :class="{
                'bg-[#d3d9e0]': active,
                'hover:bg-[#e0e4e9]': showHoverStyle
            }" @click="onMenuClick">
            <el-icon :size="size" :color="active ? activeColor ? activeColor : color : color">
                <component :is="CurrentIcon"></component>
            </el-icon>
        </div>
    </el-tooltip>
</template>
<script setup lang="ts">
import { computed, h } from 'vue'
import type { Component } from 'vue'
import SvgIcon from '../SvgIcon/index.vue';
type IIconComponent = string | Component;
const props = withDefaults(defineProps<{
    active?: boolean;
    icon: IIconComponent;
    activeIcon?: IIconComponent;
    darkIcon?: IIconComponent;
    darkActiveIcon?: IIconComponent;
    color?: string; // svg
    activeColor?: string; // svg or icon
    size?: string; // svg or icon
    width?: number | string; // img
    height?: number | string; // img
    tooltip?: string;
    showHoverStyle?: boolean;
}>(), {
    showHoverStyle: true
});
const emit = defineEmits(['click']);
const isImg = (source: IIconComponent) => {
    return typeof source === 'string' && /\.(jpg|jpeg|png|gif|bmp|svg)$/i.test(source);
};
const isSvg = (source: IIconComponent) => {
    return typeof source === 'string' && !isImg(source);
};
const isComp = (source: IIconComponent) => {
    return !isImg(source) && !isSvg(source);
};
const SvgIconComponent = (source: IIconComponent) => {
    return () => h(SvgIcon, {
        name: source,
        color: props.active ? props.activeColor ? props.activeColor : props.color : props.color,
        size: props.size || 20
    });
};
// 不支持绝对路径的图片链接以及带别名@的图片链接
const ImgComponent = (source: IIconComponent) => {
    return () => h('img', {
        src: new URL(source as string, import.meta.url).href,
        width: props.width || 20,
        height: props.height || 20
    });
};
const NormalComponent = (source: IIconComponent) => {
    return source;
};
const isDarkMode = () => {
    return false;
};
const getIcon = () => {
    return isDarkMode() ? props.darkIcon ? props.darkIcon : props.icon : props.icon;
};
const getActiveIcon = () => {
    return isDarkMode() ? props.darkActiveIcon ? props.darkActiveIcon : props.darkIcon ? props.darkIcon : props.icon : props.darkIcon ? props.darkIcon : props.icon;
}
const buildComponent = () => {
    const source = props.active ? getActiveIcon() : getIcon();
    return isComp(source) ? NormalComponent(source) : isSvg(source) ? SvgIconComponent(source) : isImg(source) ? ImgComponent(source) : '';
};
const CurrentIcon = computed(() => {
    return buildComponent();
});
const onMenuClick = (e: Event) => {
    emit('click', e);
}
</script>