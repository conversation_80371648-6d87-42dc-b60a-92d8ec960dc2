{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "sourceMap": true, "outDir": "./dist", "rootDir": ".", "strict": true, "strictPropertyInitialization": false, "moduleResolution": "node", "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src/**/*", "scripts/**/*"], "exclude": ["node_modules", "**/*.test.ts"]}